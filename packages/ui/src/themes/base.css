/* Base theme variables */
@theme {
  /* Light theme (default) */
  --color-background: oklch(0.985 0 0);
  --color-foreground: oklch(0.145 0 0);
  --color-primary: oklch(0.145 0 0);
  --color-primary-foreground: oklch(0.985 0 0);
  --color-secondary: oklch(0.922 0 0);
  --color-secondary-foreground: oklch(0.371 0 0);
  --color-muted: oklch(0.946 0 0);
  --color-muted-foreground: oklch(0.439 0 0);
  --color-accent: oklch(0.946 0 0);
  --color-accent-foreground: oklch(0.145 0 0);
  --color-destructive: oklch(0.541 0.229 27.422);
  --color-destructive-foreground: oklch(0.985 0 0);
  --color-border: oklch(0.87 0 0);
  --color-input: oklch(0.97 0 0);
  --color-ring: oklch(0.922 0 0);
  --color-card: oklch(0.985 0 0);
  --color-card-foreground: oklch(0.145 0 0);
  --color-popover: oklch(0.985 0 0);
  --color-popover-foreground: oklch(0.145 0 0);
}

/* Dark mode overrides */
@media (prefers-color-scheme: dark) {
  :root {
    --color-background: oklch(0.145 0 0);
    --color-foreground: oklch(0.985 0 0);
    --color-primary: oklch(0.985 0 0);
    --color-primary-foreground: oklch(0.145 0 0);
    --color-secondary: oklch(0.205 0 0);
    --color-secondary-foreground: oklch(0.632 0 0);
    --color-muted: oklch(0.237 0 0);
    --color-muted-foreground: oklch(0.556 0 0);
    --color-accent: oklch(0.237 0 0);
    --color-accent-foreground: oklch(0.985 0 0);
    --color-destructive: oklch(0.396 0.141 25.723);
    --color-destructive-foreground: oklch(0.985 0 0);
    --color-border: oklch(0.32 0 0);
    --color-input: oklch(0.205 0 0);
    --color-ring: oklch(0.405 0 0);
    --color-card: oklch(0.145 0 0);
    --color-card-foreground: oklch(0.985 0 0);
    --color-popover: oklch(0.145 0 0);
    --color-popover-foreground: oklch(0.985 0 0);
  }
}

/* Class-based light mode (explicit light mode) */
.light {
  /* Light mode uses the base theme variables defined above */
}

/* Class-based dark mode (overrides system preference) */
.dark {
  --color-background: oklch(0.145 0 0);
  --color-foreground: oklch(0.985 0 0);
  --color-primary: oklch(0.985 0 0);
  --color-primary-foreground: oklch(0.145 0 0);
  --color-secondary: oklch(0.205 0 0);
  --color-secondary-foreground: oklch(0.632 0 0);
  --color-muted: oklch(0.237 0 0);
  --color-muted-foreground: oklch(0.556 0 0);
  --color-accent: oklch(0.237 0 0);
  --color-accent-foreground: oklch(0.985 0 0);
  --color-destructive: oklch(0.396 0.141 25.723);
  --color-destructive-foreground: oklch(0.985 0 0);
  --color-border: oklch(0.32 0 0);
  --color-input: oklch(0.205 0 0);
  --color-ring: oklch(0.405 0 0);
  --color-card: oklch(0.145 0 0);
  --color-card-foreground: oklch(0.985 0 0);
  --color-popover: oklch(0.145 0 0);
  --color-popover-foreground: oklch(0.985 0 0);
}
