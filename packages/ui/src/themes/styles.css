/* Base styles */
* {
  border-color: var(--color-border);
}

body {
  background-color: var(--color-background);
  color: var(--color-foreground);
  font-feature-settings:
    "rlig" 1,
    "calt" 1;
}

/* Smooth theme transitions - streamlined */
* {
  transition:
    background-color 0.2s ease,
    color 0.2s ease,
    border-color 0.2s ease,
    box-shadow 0.2s ease;
}

/* Disable transitions on initial load */
.no-transition * {
  transition: none !important;
}

/* Respect user motion preferences */
@media (prefers-reduced-motion: reduce) {
  * {
    transition-duration: 0.05s !important;
  }
}
